'use client';

import React, { useMemo } from 'react';
import { Stock } from '@/types/stock';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StockStatisticsProps {
  stocks: Stock[];
}

const StockStatistics: React.FC<StockStatisticsProps> = ({ stocks }) => {
  const statistics = useMemo(() => {
    const totalStocks = stocks.length;
    
    // Status distribution
    const statusCounts = stocks.reduce((acc, stock) => {
      acc[stock.status] = (acc[stock.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Sector distribution
    const sectorCounts = stocks.reduce((acc, stock) => {
      acc[stock.sectorName] = (acc[stock.sectorName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Instrument type distribution
    const instrumentTypeCounts = stocks.reduce((acc, stock) => {
      acc[stock.instrumentType] = (acc[stock.instrumentType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top sectors
    const topSectors = Object.entries(sectorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    // Companies with websites
    const companiesWithWebsites = stocks.filter(stock => stock.website && stock.website.trim() !== '').length;
    const websitePercentage = ((companiesWithWebsites / totalStocks) * 100).toFixed(1);

    return {
      totalStocks,
      statusCounts,
      sectorCounts,
      instrumentTypeCounts,
      topSectors,
      companiesWithWebsites,
      websitePercentage
    };
  }, [stocks]);

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'A': return 'Active';
      case 'D': return 'Delisted';
      case 'S': return 'Suspended';
      default: return status;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'A': return 'default';
      case 'D': return 'destructive';
      case 'S': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      {/* Total Stocks */}
      <Card className="market-card bull-gradient">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Total Companies</CardTitle>
          <div className="text-2xl">📊</div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold financial-number text-green-600 dark:text-green-400">{statistics.totalStocks}</div>
          <p className="text-xs text-green-600/70 dark:text-green-400/70 font-medium">Listed companies</p>
        </CardContent>
      </Card>

      {/* Status Distribution */}
      <Card className="market-card neutral-gradient">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Status Distribution</CardTitle>
          <div className="text-2xl">📈</div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(statistics.statusCounts).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between">
                <Badge variant={getStatusBadgeVariant(status)} className="text-xs font-medium">
                  {status === 'A' && '🟢'}
                  {status === 'D' && '🔴'}
                  {status === 'S' && '🟡'}
                  {getStatusLabel(status)}
                </Badge>
                <span className="text-sm font-bold financial-number text-blue-600 dark:text-blue-400">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instrument Types */}
      <Card className="market-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Instrument Types</CardTitle>
          <div className="text-2xl">🏷️</div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(statistics.instrumentTypeCounts).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between">
                <span className="text-sm font-medium">{type}</span>
                <span className="text-sm font-bold financial-number text-purple-600 dark:text-purple-400">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Website Coverage */}
      <Card className="market-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Website Coverage</CardTitle>
          <div className="text-2xl">🌐</div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold financial-number text-orange-600 dark:text-orange-400">{statistics.websitePercentage}%</div>
          <p className="text-xs text-orange-600/70 dark:text-orange-400/70 font-medium">
            {statistics.companiesWithWebsites} of {statistics.totalStocks} companies
          </p>
        </CardContent>
      </Card>

      {/* Top Sectors */}
      <Card className="md:col-span-2 lg:col-span-4 market-card">
        <CardHeader className="trading-header">
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            🏆 Top Sectors by Company Count
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {statistics.topSectors.map(([sector, count], index) => (
              <div key={sector} className="text-center p-4 border rounded-lg market-card hover:shadow-md transition-all duration-200">
                <div className="text-3xl font-bold text-primary mb-2">#{index + 1}</div>
                <div className="text-sm font-semibold mb-2 text-foreground">{sector}</div>
                <div className="text-2xl font-bold financial-number text-primary">{count}</div>
                <div className="text-xs text-muted-foreground font-medium">companies</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockStatistics;
