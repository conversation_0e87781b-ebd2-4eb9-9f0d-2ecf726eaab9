'use client';

import React, { useMemo } from 'react';
import { Stock } from '@/types/stock';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StockStatisticsProps {
  stocks: Stock[];
}

const StockStatistics: React.FC<StockStatisticsProps> = ({ stocks }) => {
  const statistics = useMemo(() => {
    const totalStocks = stocks.length;
    
    // Status distribution
    const statusCounts = stocks.reduce((acc, stock) => {
      acc[stock.status] = (acc[stock.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Sector distribution
    const sectorCounts = stocks.reduce((acc, stock) => {
      acc[stock.sectorName] = (acc[stock.sectorName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Instrument type distribution
    const instrumentTypeCounts = stocks.reduce((acc, stock) => {
      acc[stock.instrumentType] = (acc[stock.instrumentType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top sectors
    const topSectors = Object.entries(sectorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    // Companies with websites
    const companiesWithWebsites = stocks.filter(stock => stock.website && stock.website.trim() !== '').length;
    const websitePercentage = ((companiesWithWebsites / totalStocks) * 100).toFixed(1);

    return {
      totalStocks,
      statusCounts,
      sectorCounts,
      instrumentTypeCounts,
      topSectors,
      companiesWithWebsites,
      websitePercentage
    };
  }, [stocks]);

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'A': return 'Active';
      case 'D': return 'Delisted';
      case 'S': return 'Suspended';
      default: return status;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'A': return 'default';
      case 'D': return 'destructive';
      case 'S': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      {/* Total Stocks */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">📊</div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalStocks}</div>
          <p className="text-xs text-muted-foreground">Listed companies</p>
        </CardContent>
      </Card>

      {/* Status Distribution */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Status Distribution</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">📈</div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(statistics.statusCounts).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between">
                <Badge variant={getStatusBadgeVariant(status)} className="text-xs">
                  {getStatusLabel(status)}
                </Badge>
                <span className="text-sm font-medium">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instrument Types */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Instrument Types</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">🏷️</div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(statistics.instrumentTypeCounts).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between">
                <span className="text-sm">{type}</span>
                <span className="text-sm font-medium">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Website Coverage */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Website Coverage</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">🌐</div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.websitePercentage}%</div>
          <p className="text-xs text-muted-foreground">
            {statistics.companiesWithWebsites} of {statistics.totalStocks} companies
          </p>
        </CardContent>
      </Card>

      {/* Top Sectors */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="text-lg">Top Sectors by Company Count</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {statistics.topSectors.map(([sector, count], index) => (
              <div key={sector} className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-primary">#{index + 1}</div>
                <div className="text-sm font-medium mb-1">{sector}</div>
                <div className="text-lg font-bold">{count}</div>
                <div className="text-xs text-muted-foreground">companies</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockStatistics;
