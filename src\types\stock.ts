export interface Stock {
  id: number;
  companyName: string;
  symbol: string;
  securityName: string;
  status: string;
  companyEmail: string;
  website: string;
  sectorName: string;
  regulatoryBody: string;
  instrumentType: string;
}

export type StatusType = 'A' | 'D' | 'S';
export type SortField = 'companyName' | 'symbol' | 'sectorName' | 'instrumentType' | 'status';
export type SortDirection = 'asc' | 'desc';
