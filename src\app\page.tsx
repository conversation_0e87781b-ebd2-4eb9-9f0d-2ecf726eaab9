import StockListing from '@/components/StockListing';
import StockStatistics from '@/components/StockStatistics';
import ThemeToggle from '@/components/ThemeToggle';
import { Stock } from '@/types/stock';
import stocksData from '@/stocks.json';

export default function Home() {
  const stocks: Stock[] = stocksData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <ThemeToggle />
      <div className="container mx-auto px-4 py-8">
        <StockStatistics stocks={stocks} />
        <StockListing stocks={stocks} />
      </div>
    </div>
  );
}
