# Stock Market Data Dashboard

A comprehensive stock market data dashboard built with Next.js, TypeScript, and shadcn/ui components. This application provides an interactive interface to browse, filter, and analyze stock market data from Nepal.

## Features

### 📊 Data Visualization
- **Statistics Dashboard**: Overview cards showing total companies, status distribution, instrument types, and website coverage
- **Top Sectors**: Visual representation of the most active sectors by company count

### 🔍 Advanced Filtering
- **Search**: Real-time search across company names, symbols, and security names
- **Sector Filter**: Filter companies by their business sector
- **Instrument Type Filter**: Filter by equity, bonds, or other instrument types
- **Status Filter**: Filter by Active (A), Delisted (D), or Suspended (S) status
- **Clear Filters**: One-click reset of all filters

### 📋 Data Table
- **Sortable Columns**: Click on column headers to sort by company name, symbol, sector, instrument type, or status
- **Pagination**: Navigate through large datasets with pagination controls (20 items per page)
- **Status Badges**: Color-coded badges for easy status identification
- **Clickable Rows**: Click on any row to view detailed company information

### 🔍 Detailed View
- **Modal Interface**: Click on any company row to open a detailed modal
- **Complete Information**: View all available company data including contact information
- **Quick Actions**: Direct links to company websites and email contacts

### 🎨 Modern UI/UX
- **shadcn/ui Components**: Beautiful, accessible components with consistent design
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Dark/Light Mode Support**: Automatic theme detection and switching
- **Loading States**: Smooth user experience with proper loading indicators

## Technology Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui
- **Icons**: Built-in emoji icons for better accessibility

## Data Structure

The application works with stock data containing:
- Company information (name, symbol, security name)
- Business details (sector, instrument type, regulatory body)
- Status information (active, delisted, suspended)
- Contact details (email, website)

## Getting Started

1. **Install dependencies**:
```bash
npm install
```

2. **Run the development server**:
```bash
npm run dev
```

3. **Open your browser**:
Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/
│   ├── page.tsx          # Main page component
│   ├── layout.tsx        # Root layout
│   └── globals.css       # Global styles
├── components/
│   ├── ui/               # shadcn/ui components
│   ├── StockListing.tsx  # Main data table component
│   ├── StockDetailModal.tsx # Detailed view modal
│   └── StockStatistics.tsx  # Statistics dashboard
├── types/
│   └── stock.ts          # TypeScript type definitions
└── stocks.json           # Stock data file
```

## Usage Examples

### Filtering Data
1. Use the search box to find specific companies
2. Select a sector from the dropdown to view companies in that sector
3. Filter by instrument type (typically "Equity")
4. Filter by status to see only active, delisted, or suspended companies

### Viewing Details
1. Click on any company row in the table
2. A modal will open with complete company information
3. Use the action buttons to visit the company website or send an email

### Sorting Data
1. Click on any column header to sort by that field
2. Click again to reverse the sort order
3. Sort indicators (↑↓) show the current sort direction

## Customization

The application is highly customizable:
- Modify `src/stocks.json` to use your own data
- Update the `Stock` interface in `src/types/stock.ts` for different data structures
- Customize styling through Tailwind CSS classes
- Add new filter options by extending the filter components

## Performance Features

- **Client-side filtering**: Fast, responsive filtering without server requests
- **Pagination**: Efficient rendering of large datasets
- **Memoized calculations**: Optimized performance for statistics and filtering
- **Lazy loading**: Components load only when needed

## Accessibility

- **Keyboard navigation**: Full keyboard support for all interactive elements
- **Screen reader friendly**: Proper ARIA labels and semantic HTML
- **Color contrast**: High contrast colors for better readability
- **Focus indicators**: Clear focus states for keyboard users
