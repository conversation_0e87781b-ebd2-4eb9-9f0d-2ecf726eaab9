'use client';

import React from 'react';
import { Stock } from '@/types/stock';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface StockDetailModalProps {
  stock: Stock | null;
  isOpen: boolean;
  onClose: () => void;
}

const StockDetailModal: React.FC<StockDetailModalProps> = ({ stock, isOpen, onClose }) => {
  if (!isOpen || !stock) return null;

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'A': return 'default';
      case 'D': return 'destructive';
      case 'S': return 'secondary';
      default: return 'outline';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'A': return 'Active';
      case 'D': return 'Delisted';
      case 'S': return 'Suspended';
      default: return status;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-2xl font-bold">Stock Details</CardTitle>
          <Button variant="outline" size="sm" onClick={onClose}>
            ✕
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Company Information */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">{stock.companyName}</h3>
              <Badge variant={getStatusBadgeVariant(stock.status)} className="text-sm">
                {getStatusLabel(stock.status)}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Symbol</label>
                  <p className="font-mono font-bold text-lg">{stock.symbol}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Security Name</label>
                  <p>{stock.securityName}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Sector</label>
                  <p>{stock.sectorName}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Instrument Type</label>
                  <p>{stock.instrumentType}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Regulatory Body</label>
                  <p>{stock.regulatoryBody}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Company ID</label>
                  <p>{stock.id}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="border-t pt-4">
            <h4 className="text-lg font-semibold mb-3">Contact Information</h4>
            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Email</label>
                <p>
                  {stock.companyEmail ? (
                    <a 
                      href={`mailto:${stock.companyEmail}`}
                      className="text-blue-600 hover:underline"
                    >
                      {stock.companyEmail}
                    </a>
                  ) : (
                    <span className="text-muted-foreground">Not available</span>
                  )}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-muted-foreground">Website</label>
                <p>
                  {stock.website ? (
                    <a 
                      href={stock.website.startsWith('http') ? stock.website : `https://${stock.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {stock.website}
                    </a>
                  ) : (
                    <span className="text-muted-foreground">Not available</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="border-t pt-4 flex gap-2">
            {stock.website && (
              <Button asChild>
                <a 
                  href={stock.website.startsWith('http') ? stock.website : `https://${stock.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Visit Website
                </a>
              </Button>
            )}
            
            {stock.companyEmail && (
              <Button variant="outline" asChild>
                <a href={`mailto:${stock.companyEmail}`}>
                  Send Email
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockDetailModal;
