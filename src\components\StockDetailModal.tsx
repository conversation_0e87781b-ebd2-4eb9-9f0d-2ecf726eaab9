'use client';

import React from 'react';
import { Stock } from '@/types/stock';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface StockDetailModalProps {
  stock: Stock | null;
  isOpen: boolean;
  onClose: () => void;
}

const StockDetailModal: React.FC<StockDetailModalProps> = ({ stock, isOpen, onClose }) => {
  if (!isOpen || !stock) return null;

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'A': return 'default';
      case 'D': return 'destructive';
      case 'S': return 'secondary';
      default: return 'outline';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'A': return 'Active';
      case 'D': return 'Delisted';
      case 'S': return 'Suspended';
      default: return status;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto market-card shadow-2xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 trading-header">
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-green-600 bg-clip-text text-transparent">
            📊 Stock Details
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onClose} className="hover:bg-destructive/10">
            ✕
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Company Information */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold text-foreground">{stock.companyName}</h3>
              <Badge variant={getStatusBadgeVariant(stock.status)} className="text-sm font-medium">
                {stock.status === 'A' && '🟢'}
                {stock.status === 'D' && '🔴'}
                {stock.status === 'S' && '🟡'}
                {getStatusLabel(stock.status)}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">🏷️ Symbol</label>
                  <p className="stock-symbol text-xl text-primary">{stock.symbol}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">📄 Security Name</label>
                  <p className="font-medium">{stock.securityName}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">🏭 Sector</label>
                  <p className="font-medium text-blue-600 dark:text-blue-400">{stock.sectorName}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">📊 Instrument Type</label>
                  <p className="font-medium text-purple-600 dark:text-purple-400">{stock.instrumentType}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">🏛️ Regulatory Body</label>
                  <p className="font-medium">{stock.regulatoryBody}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">🆔 Company ID</label>
                  <p className="financial-number font-bold text-primary">{stock.id}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="border-t border-border/50 pt-4">
            <h4 className="text-lg font-semibold mb-3 flex items-center gap-2">
              📞 Contact Information
            </h4>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">📧 Email</label>
                <p>
                  {stock.companyEmail ? (
                    <a
                      href={`mailto:${stock.companyEmail}`}
                      className="text-primary hover:text-primary/80 hover:underline font-medium transition-colors"
                    >
                      {stock.companyEmail}
                    </a>
                  ) : (
                    <span className="text-muted-foreground">Not available</span>
                  )}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">🌐 Website</label>
                <p>
                  {stock.website ? (
                    <a
                      href={stock.website.startsWith('http') ? stock.website : `https://${stock.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80 hover:underline font-medium transition-colors"
                    >
                      {stock.website}
                    </a>
                  ) : (
                    <span className="text-muted-foreground">Not available</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="border-t border-border/50 pt-4 flex gap-3">
            {stock.website && (
              <Button asChild className="bg-primary hover:bg-primary/90">
                <a
                  href={stock.website.startsWith('http') ? stock.website : `https://${stock.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  🌐 Visit Website
                </a>
              </Button>
            )}

            {stock.companyEmail && (
              <Button variant="outline" asChild className="hover:bg-primary/10">
                <a href={`mailto:${stock.companyEmail}`}>
                  📧 Send Email
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockDetailModal;
